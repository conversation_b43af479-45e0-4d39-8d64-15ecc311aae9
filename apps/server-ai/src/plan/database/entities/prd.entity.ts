import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Session } from './session.entity';

@Entity()
export class PRD {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  title: string;

  @Column('text')
  originalContent: string;

  @Column('text', { nullable: true })
  structuredContent: string;

  @Column({ type: 'int', default: 1 })
  version: number;

  @Column('simple-json', { nullable: true })
  metadata: any;

  @ManyToOne(() => Session, (session) => session.prds)
  session: Session;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

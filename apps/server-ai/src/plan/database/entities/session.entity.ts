import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from 'typeorm';
import { Task } from './task.entity';
import { PRD } from './prd.entity';

export enum SessionStatus {
  // 需求分析阶段 - 系统正在分析PRD内容
  REQUIREMENT_ANALYSIS = 'requirement_analysis',
  // 任务规划阶段 - 系统正在生成任务列表
  TASK_PLANNING = 'task_planning',
  // 待开发阶段 - 任务已生成，等待开发执行
  PENDING_DEVELOPMENT = 'pending_development',
  // 开发中阶段 - 任务正在被执行
  IN_DEVELOPMENT = 'in_development',
  // 已完成阶段 - 所有任务完成
  COMPLETED = 'completed',
  // 失败状态 - 工作流执行失败
  FAILED = 'failed',
  // 已归档阶段 - 会话被归档
  ARCHIVED = 'archived',

  // 兼容旧状态，映射到 PENDING_DEVELOPMENT
  IN_PROGRESS = 'in_progress',
  // 兼容旧状态，映射到 REQUIREMENT_ANALYSIS
  PENDING = 'pending',
  // 兼容旧状态，映射到 TASK_PLANNING
  RUNNING = 'running',
}

@Entity()
export class Session {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  title: string;

  @Column({
    type: 'enum',
    enum: SessionStatus,
    default: SessionStatus.REQUIREMENT_ANALYSIS,
  })
  status: SessionStatus;

  @Column({ nullable: true })
  frontendPageId: string;

  @Column({ nullable: true })
  backendApiId: string;

  @Column({ nullable: true })
  previewUrl: string;

  @Column({ nullable: true })
  threadId: string;

  // 从 Workflow 合并的字段
  @Column('text', { nullable: true })
  input: string;

  @Column('text', { nullable: true })
  finalReport: string;

  @Column('simple-json', { nullable: true })
  currentPlan: any;

  @Column('simple-json', { nullable: true })
  observations: any[];

  @Column({ default: 0 })
  planIterations: number;

  @Column('simple-json', { nullable: true })
  config: any;

  @OneToMany(() => Task, (task) => task.session)
  tasks: Task[];

  @OneToMany(() => PRD, (prd) => prd.session)
  prds: PRD[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt?: Date;
}

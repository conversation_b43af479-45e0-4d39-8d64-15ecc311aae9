import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum MessageRole {
  USER = 'user',
  ASSISTANT = 'assistant',
  TOOL = 'tool',
}

export enum AgentType {
  COORDINATOR = 'coordinator',
  PLANNER = 'planner',
  RESEARCHER = 'researcher',
  CODER = 'coder',
  ANALYST = 'analyst',
  REPORTER = 'reporter',
}

@Entity()
export class Message {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  threadId: string;

  @Column({
    type: 'enum',
    enum: MessageRole,
    default: MessageRole.USER,
  })
  role: MessageRole;

  @Column({
    type: 'enum',
    enum: AgentType,
    nullable: true,
  })
  agent?: AgentType;

  @Column('text')
  content: string;

  @Column('simple-json', { nullable: true })
  toolCalls?: any[];

  @Column('simple-json', { nullable: true })
  options?: { text: string; value: string }[];

  @Column({ nullable: true })
  finishReason?: string;

  @Column({ nullable: true })
  interruptFeedback?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

import { Command } from '@langchain/langgraph';
import { AIMessage, HumanMessage } from '@langchain/core/messages';
import { createStructuredLLM } from '../utils/llm.utils';
import { NodeNames, TaskType, TaskPriority, TaskStatus } from '../constants';
import { StateAnnotation, Task } from '../types/state.types';
import {
  createMessageWithImages,
  createErrorMessage,
} from '../utils/message.utils';

/**
 * 前端规划器节点 - 生成前端开发任务
 * 专门负责分析PRD中的前端需求并生成相应的开发任务
 */
export async function frontendPlannerNode(
  state: typeof StateAnnotation.State,
): Promise<Command> {
  console.log('FRONTEND_PLANNER节点开始生成前端任务...');

  const llm = createStructuredLLM();

  // 构建前端任务规划提示
  const promptText = `
你是一个专业的前端架构师和开发专家，精通现代前端技术栈。你的任务是基于结构化的PRD文档，分析前端需求并生成详细的前端开发任务列表。

## 结构化PRD文档
${state.structuredPRD}

${
  state.planFeedback
    ? `
## 用户反馈信息
用户对任务规划提供了以下反馈，请在重新规划时重点考虑：
${state.planFeedback}

请基于这些反馈信息调整前端任务规划，确保满足用户的具体要求。
`
    : ''
}

${
  state.images && state.images.length > 0
    ? `
## 相关图片信息
PRD中包含以下图片，请在任务规划时重点考虑：
${state.images.map((img, index) => `图片${index + 1}: ${img.url}${img.error ? ` (处理失败: ${img.error})` : ' (已处理)'}`).join('\n')}

如果图片包含UI原型、界面设计或交互流程，请在任务的details字段中详细描述相关的实现要求。
`
    : ''
}

## 技术栈说明
- **前端框架**: 基于AMIS的低代码JSON配置
- **组件库**: AMIS内置组件 + 自定义组件
- **状态管理**: AMIS内置状态管理
- **样式方案**: CSS-in-JS + 主题定制
- **构建工具**: Webpack/Vite
- **开发规范**: TypeScript + ESLint + Prettier

## 任务规划原则
1. **原子性**: 每个任务应该是独立的、可测试的功能单元
2. **渐进式**: 从基础组件到复杂功能，循序渐进
3. **可复用**: 优先考虑组件的复用性和扩展性
4. **用户体验**: 重视交互体验和响应式设计
5. **性能优化**: 考虑加载速度和运行效率

## 任务分类指导
### 基础设施任务 (优先级: high)
- 项目初始化和配置
- 基础组件库搭建
- 路由和导航配置
- 主题和样式系统

### 核心功能任务 (优先级: high/medium)
- 主要页面和组件开发
- 数据展示和交互功能
- 表单处理和验证
- 状态管理实现

### 增强功能任务 (优先级: medium/low)
- 高级交互效果
- 性能优化
- 响应式适配
- 无障碍访问支持

## 响应格式要求
请严格按照以下JSON格式返回前端任务列表：

\`\`\`json
{
  "tasks": [
    {
      "id": 1,
      "title": "任务标题",
      "description": "任务描述",
      "status": "pending",
      "prompt": "给开发者的详细指令",
      "dependencies": [依赖的任务ID数组],
      "priority": "high|medium|low",
      "details": "详细的实现指导，包括技术细节、设计要求等",
      "testStrategy": "测试和验证方法",
      "type": "frontend"
    }
  ],
  "summary": {
    "totalTasks": 任务总数,
    "techStack": ["AMIS", "TypeScript", "CSS-in-JS"],
    "keyComponents": ["主要组件列表"],
    "dependencies": "对后端API的依赖说明"
  }
}
\`\`\`

## 任务示例参考
\`\`\`json
{
  "id": 1,
  "title": "初始化AMIS项目结构",
  "description": "创建基于AMIS的前端项目基础架构",
  "status": "pending",
  "prompt": "请使用AMIS框架创建项目基础结构，包括页面路由、主题配置和基础组件",
  "dependencies": [],
  "priority": "high",
  "details": "1. 安装AMIS依赖包\n2. 配置webpack/vite构建\n3. 设置TypeScript配置\n4. 创建基础页面模板\n5. 配置主题变量和样式",
  "testStrategy": "验证项目能够正常启动，页面路由正常工作，基础样式正确加载",
  "type": "frontend"
}
\`\`\`

## 重要提示
1. 任务ID从1开始连续编号
2. 依赖关系要合理，避免循环依赖
3. 每个任务的details字段要包含具体的技术实现指导
4. 考虑AMIS框架的特点，优先使用配置化方案
5. 如果PRD中有具体的UI设计要求，要在details中详细说明
6. 估算工时要现实合理，考虑开发和测试时间

现在请基于以上PRD内容，生成详细的前端开发任务列表：
`;

  try {
    // 使用LLM生成前端任务
    let response: AIMessage;

    if (state.images && state.images.length > 0) {
      const messageContent = createMessageWithImages(promptText, state.images);
      response = await llm.invoke([
        new HumanMessage({
          content: messageContent,
        }),
      ]);
    } else {
      response = await llm.invoke(promptText);
    }

    console.log('FRONTEND_PLANNER节点完成前端任务生成', response);

    // 解析LLM响应
    const responseContent =
      typeof response.content === 'string'
        ? response.content
        : response.content.toString();

    // 提取JSON内容
    const jsonMatch = responseContent.match(/```json\s*([\s\S]*?)\s*```/);
    let parsedResponse: { tasks: Task[]; summary: any };

    if (jsonMatch) {
      try {
        parsedResponse = JSON.parse(jsonMatch[1]);
      } catch (parseError) {
        console.error('前端任务JSON解析失败:', parseError);
        throw new Error('前端任务生成失败：JSON格式无效');
      }
    } else {
      // 尝试直接解析整个响应
      try {
        parsedResponse = JSON.parse(responseContent);
      } catch (parseError) {
        console.error('无法解析前端任务响应为JSON:', responseContent);
        throw new Error('前端任务响应格式不正确');
      }
    }

    // 验证和标准化任务数据
    if (!parsedResponse.tasks || !Array.isArray(parsedResponse.tasks)) {
      throw new Error('前端任务列表格式无效');
    }

    // 标准化任务数据
    const standardizedTasks = parsedResponse.tasks.map((task, index) => ({
      ...task,
      id: task.id || index + 1,
      type: TaskType.FRONTEND,
      status: TaskStatus.PENDING,
      priority: task.priority || TaskPriority.MEDIUM,
    }));

    console.log(`前端任务生成完成，共${standardizedTasks.length}个任务`);

    // 转换为JSON字符串存储
    const frontendTaskJSON = JSON.stringify(standardizedTasks, null, 2);

    return new Command({
      goto: NodeNames.PLANNER_FEEDBACK,
      update: {
        frontendTaskJSON: frontendTaskJSON,
        taskJSON: frontendTaskJSON, // 暂时只有前端任务
        processingStatus: `前端任务规划完成，共生成${standardizedTasks.length}个任务，等待用户审核`,
      },
    });
  } catch (error) {
    console.error('FRONTEND_PLANNER节点处理失败:', error);

    // 创建默认的前端任务作为fallback
    const fallbackTasks: Task[] = [
      {
        id: 1,
        title: '前端项目初始化',
        description: '创建基于AMIS的前端项目基础结构',
        status: TaskStatus.PENDING,
        prompt: '请初始化AMIS前端项目，包括基础配置和目录结构',
        dependencies: [],
        priority: TaskPriority.HIGH,
        details: '由于自动任务生成失败，请手动创建前端项目基础结构',
        testStrategy: '验证项目能够正常启动和运行',
        type: TaskType.FRONTEND,
      },
    ];

    const fallbackJSON = JSON.stringify(fallbackTasks, null, 2);

    return new Command({
      goto: NodeNames.PLANNER_FEEDBACK,
      update: {
        frontendTaskJSON: fallbackJSON,
        taskJSON: fallbackJSON, // 暂时只有前端任务
        error: createErrorMessage(error, '前端任务规划'),
        processingStatus: '前端任务规划遇到问题，已生成基础任务，等待用户审核',
      },
    });
  }
}

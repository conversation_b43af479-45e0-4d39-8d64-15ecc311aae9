import { Command, interrupt } from '@langchain/langgraph';
import { NodeNames } from '../constants';
import { StateAnnotation } from '../types/state.types';

/**
 * PRD反馈节点 - 处理PRD阶段的人工反馈
 * 当PRD信息不完整时，等待用户补充信息
 */
export function prdFeedbackNode(state: typeof StateAnnotation.State): Command {
  console.log('等待PRD parser阶段人工反馈...');

  // 解析PRD分析结果
  let prdAnalysis: any = {};
  try {
    if (state.prdAnalysis) {
      prdAnalysis = JSON.parse(state.prdAnalysis);
    }
  } catch (error) {
    console.error('解析PRD分析结果失败:', error);
  }

  const feedback = interrupt({
    question: '检测到PRD信息不完整，请补充缺少的内容',
    prdAnalysis: state.prdAnalysis,
    amisForm: prdAnalysis.content || null,
    action: '请选择: approve(批准继续) 或 revise(修改PRD)',
    instructions: '如果需要修改，请在feedback字段中提供具体的补充信息',
  });

  // 当恢复执行时，这里的逻辑会被执行
  const feedbackContent = feedback?.feedback || '';
  console.log('收到PRD反馈:', feedbackContent);

  // 根据反馈决定下一步
  if (feedbackContent === 'approve') {
    console.log('用户批准PRD，继续进行前端规划');
    return new Command({
      goto: NodeNames.FRONTEND_PLANNER,
      update: {
        processingStatus: '用户已批准PRD，开始前端任务规划',
      },
    });
  } else {
    // 如果需要修改，回到PRD_PARSER节点
    console.log('用户要求修改PRD，返回PRD解析节点');
    return new Command({
      goto: NodeNames.PRD_PARSER,
      update: {
        prdFeedback: feedbackContent,
        originalPRD:
          state.originalPRD +
          '\n\n用户补充信息：\n' +
          (JSON.stringify(feedback?.feedback, undefined, 2) || ''),
        processingStatus: '用户提供了补充信息，重新解析PRD',
      },
    });
  }
}

/**
 * 规划器反馈节点 - 处理任务规划阶段的人工反馈
 * 当任务规划完成后，等待用户审核和确认
 */
export function plannerFeedbackNode(
  state: typeof StateAnnotation.State,
): Command {
  console.log('等待Planner阶段人工反馈...');

  // 解析任务统计信息
  let frontendTasks: any[] = [];
  let backendTasks: any[] = [];
  let allTasks: any[] = [];

  try {
    if (state.frontendTaskJSON) {
      frontendTasks = JSON.parse(state.frontendTaskJSON);
    }
    if (state.backendTaskJSON) {
      backendTasks = JSON.parse(state.backendTaskJSON);
    }

    if (state.taskJSON) {
      allTasks = JSON.parse(state.taskJSON);
    }
  } catch (error) {
    console.error('解析任务JSON失败:', error);
  }

  // 计算总体统计信息
  const totalTasks = allTasks.length;

  const highPriorityTasks = allTasks.filter(
    (task) => task.priority === 'high',
  ).length;

  const feedback = interrupt({
    question: '请审核任务规划结果和技术实现方案',
    summary: {
      totalTasks,
      frontendTasks: frontendTasks.length,
      backendTasks: backendTasks.length,
      highPriorityTasks,
    },
    taskBreakdown: {
      frontend: frontendTasks.map((task) => ({
        id: task.id,
        title: task.title,
        priority: task.priority,
      })),
      backend: backendTasks.map((task) => ({
        id: task.id,
        title: task.title,
        priority: task.priority,
      })),
    },
    taskJSON: state.taskJSON,
    structuredPRD: state.structuredPRD,
    processingStatus: state.processingStatus,
    action: '请选择: approve(批准) 或 revise(修改)',
    instructions: '如果需要修改，请在feedback字段中提供具体的修改建议',
  });

  // 当恢复执行时，这里的逻辑会被执行
  const feedbackContent = feedback?.feedback || '';
  console.log('收到Planner反馈:', feedback);

  // 根据反馈决定下一步
  if (feedbackContent === 'approve') {
    // 检查是否只有前端任务（还没有后端任务）
    if (backendTasks.length === 0 && frontendTasks.length > 0) {
      console.log('用户批准前端任务规划，开始后端任务规划');
      return new Command({
        goto: NodeNames.BACKEND_PLANNER,
        update: {
          processingStatus: '前端任务规划已获得批准，开始后端任务规划',
        },
      });
    } else {
      console.log('用户批准完整任务规划，流程完成');
      return new Command({
        goto: 'END',
        update: {
          processingStatus: `任务规划已完成并获得批准！共${totalTasks}个任务`,
        },
      });
    }
  } else {
    console.log('用户要求修改任务规划:', feedbackContent);

    // 简单的反馈分析，决定回到哪个规划节点
    if (
      feedbackContent.toLowerCase().includes('前端') ||
      feedbackContent.toLowerCase().includes('frontend')
    ) {
      return new Command({
        goto: NodeNames.FRONTEND_PLANNER,
        update: {
          planFeedback: feedbackContent,
          processingStatus: '用户要求修改前端任务规划',
        },
      });
    } else if (
      feedbackContent.toLowerCase().includes('后端') ||
      feedbackContent.toLowerCase().includes('backend')
    ) {
      return new Command({
        goto: NodeNames.BACKEND_PLANNER,
        update: {
          planFeedback: feedbackContent,
          processingStatus: '用户要求修改后端任务规划',
        },
      });
    } else {
      // 默认回到前端规划节点重新开始
      return new Command({
        goto: NodeNames.FRONTEND_PLANNER,
        update: {
          planFeedback: feedbackContent,
          processingStatus: '用户要求修改任务规划，重新开始规划流程',
        },
      });
    }
  }
}

import { Annotation, messagesStateReducer } from '@langchain/langgraph';
import { BaseMessage, AIMessage } from '@langchain/core/messages';
import { TaskType, TaskPriority, TaskStatus } from '../constants';

/**
 * 图片信息接口
 */
export interface ImageInfo {
  url: string;
  base64: string;
  error?: string;
}

/**
 * 任务接口定义
 */
export interface Task {
  id: number;
  title: string;
  description: string;
  status: TaskStatus;
  prompt: string;
  dependencies: number[];
  priority: TaskPriority;
  details: string;
  testStrategy: string;
  type: TaskType;
}

/**
 * PRD解析响应接口
 */
export interface PRDParserResponse {
  type: 'complete' | 'incomplete';
  content: string;
}

/**
 * 任务规划响应接口
 */
export interface PlannerResponse {
  frontendTasks: Task[];
  backendTasks: Task[];
  summary: {
    totalTasks: number;
    estimatedDuration: string;
    techStack: {
      frontend: string[];
      backend: string[];
    };
  };
}

/**
 * 状态注解定义
 * 定义工作流图中的状态结构
 */
export const StateAnnotation = Annotation.Root({
  // 原始PRD输入
  originalPRD: Annotation<string>,

  // 结构化PRD
  structuredPRD: Annotation<string>,

  // 任务JSON
  taskJSON: Annotation<string>,

  // 前端任务JSON
  frontendTaskJSON: Annotation<string>,

  // 后端任务JSON
  backendTaskJSON: Annotation<string>,

  // PRD反馈
  prdFeedback: Annotation<string>,

  // PRD分析结果
  prdAnalysis: Annotation<string>,

  // 规划反馈
  planFeedback: Annotation<string>,

  // 是否为PRD请求
  isPRDRequest: Annotation<boolean>,

  // 文档内容
  documentContent: Annotation<string>,

  // 图片信息
  images: Annotation<ImageInfo[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),

  // 消息历史
  messages: Annotation<(BaseMessage | AIMessage)[]>({
    reducer: messagesStateReducer,
    default: () => [],
  }),

  // 错误信息
  error: Annotation<string>,

  // 处理状态
  processingStatus: Annotation<string>,

  // 当前节点
  currentNode: Annotation<string>,
});

/**
 * 状态类型定义
 */
export type GraphState = typeof StateAnnotation.State;
